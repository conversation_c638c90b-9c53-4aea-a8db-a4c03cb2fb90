/*#include <stdio.h>

struct Block {
    int size;
    int occupied;
};

struct Process {
    int size;
    int allocatedBlock;
};

void firstFit(struct Block blocks[], int numBlocks, struct Process processes[], int numProcesses) {
    for (int i = 0; i < numProcesses; i++) {
        for (int j = 0; j < numBlocks; j++) {
            if (blocks[j].size >= processes[i].size && !blocks[j].occupied) {
                processes[i].allocatedBlock = j;
                blocks[j].occupied = 1;
                break; // Allocation successful, move to next process
            }
        }
    }
}

void bestFit(struct Block blocks[], int numBlocks, struct Process processes[], int numProcesses) {
    for (int i = 0; i < numProcesses; i++) {
        int bestFitIndex = -1;
        for (int j = 0; j < numBlocks; j++) {
            if (blocks[j].size >= processes[i].size && !blocks[j].occupied) {
                if (bestFitIndex == -1 || blocks[j].size < blocks[bestFitIndex].size) {
                    bestFitIndex = j;
                }
            }
        }
        if (bestFitIndex != -1) {
            processes[i].allocatedBlock = bestFitIndex;
            blocks[bestFitIndex].occupied = 1;
        }
    }
}

void worstFit(struct Block blocks[], int numBlocks, struct Process processes[], int numProcesses) {
    for (int i = 0; i < numProcesses; i++) {
        int worstFitIndex = -1;
        for (int j = 0; j < numBlocks; j++) {
            if (blocks[j].size >= processes[i].size && !blocks[j].occupied) {
                if (worstFitIndex == -1 || blocks[j].size > blocks[worstFitIndex].size) {
                    worstFitIndex = j;
                }
            }
        }
        if (worstFitIndex != -1) {
            processes[i].allocatedBlock = worstFitIndex;
            blocks[worstFitIndex].occupied = 1;
        }
    }
}

void printResults(struct Process processes[], int numProcesses) {
    printf("\nProcess No.\tProcess Size\tBlock no.\n");
    for (int i = 0; i < numProcesses; i++) {
        printf("%d \t\t\t %d \t\t\t", i+1, processes[i].size);
        if (processes[i].allocatedBlock != -1)
            printf("%d\n", processes[i].allocatedBlock + 1);
        else
            printf("Not Allocated\n");
    }
}

int main() {
    struct Block blocks[] = {{20, 0}, {100, 0}, {40, 0}, {200, 0}, {10, 0}};
    struct Process processes[] = {{90, -1}, {50, -1}, {30, -1}, {40, -1}};
    int numBlocks = sizeof(blocks) / sizeof(blocks[0]);
    int numProcesses = sizeof(processes) / sizeof(processes[0]);

    // First Fit
    printf("First Fit Allocation:\n");
    firstFit(blocks, numBlocks, processes, numProcesses);
    printResults(processes, numProcesses);

    // Reset blocks and processes for other algorithms
    for (int i = 0; i < numBlocks; i++) {
        blocks[i].occupied = 0;
    }
    for (int i = 0; i < numProcesses; i++) {
        processes[i].allocatedBlock = -1;
    }

    // Best Fit
    printf("\nBest Fit Allocation:\n");
    bestFit(blocks, numBlocks, processes, numProcesses);
    printResults(processes, numProcesses);

    // Reset blocks and processes for other algorithms
    for (int i = 0; i < numBlocks; i++) {
        blocks[i].occupied = 0;
    }
    for (int i = 0; i < numProcesses; i++) {
        processes[i].allocatedBlock = -1;
    }

    // Worst Fit
    printf("\nWorst Fit Allocation:\n");
    worstFit(blocks, numBlocks, processes, numProcesses);
    printResults(processes, numProcesses);

    return 0;
}

#include <stdio.h>
#include<unistd.h>
int main()
{
fork();
fork()&&fork()||fork();
fork();
printf(“Yes ”);
return 0;
}
#include <stdio.h>
#include <string.h>

int main() {
  char str[100];
  int i, ctr, ch;

  printf("\n\nConvert a string to lowercase and uppercase :\n");
  printf("--------------------------------------------------------------\n");
  printf("Input the string : ");
  fgets(str, sizeof str, stdin);

  i = strlen(str);
  ctr = i;

  printf("\nThe given sentence is : %s", str);

  printf("\nAfter Case changed the string is: ");
  for (i = 0; i < ctr; i++) {
    ch=islower(str[i]) ? toupper(str[i]) : tolower(str[i]);
    printf("%c", ch);
  }

  printf("\n");
  return 0;
}
#include <stdio.h>
int n;
struct Student{
    int regno;
    char na[50];
    char br[50];
    char dob[11];
    int ma[5];
    char gr;
};

int main() {

    printf("Enter the number of students");
    scanf("%d",&n);

    struct Student st[n];

    for (int i=0;i<n;i++) {
        printf("\nStudent %d\n", i+1);

        printf("Enter Registration Number");
        scanf("%d",&st[i].regno);

        printf("Enter Name");
        scanf("%s",st[i].na);

        printf("Enter Branch");
        scanf("%s",st[i].br);

        printf("Enter Date of Birth (DD-MM-YYYY)");
        scanf("%s",st[i].dob);

        printf("Enter Marks of 5 Subjects");
        for (int j=0;j<n;j++) {
            scanf("%d",&st[i].ma[j]);
        }

        int tm=0;
        for (int j=0;j<n; j++) {
            tm+=st[i].ma[j];
        }

        float am=tm/n;

        if (am>=90) {
            st[i].gr='A';
        } else if (am>= 80) {
            st[i].gr='B';
        } else if (am>= 70) {
            st[i].gr='C';
        } else if (am>= 60) {
            st[i].gr='D';
        } else {
            st[i].gr='F';
        }
    }

    printf("\nStudent Details:\n");
    for (int i=0;i<n;i++) {
        printf("\nStudent %d\n", i+1);
        printf("registration Number: %d\n", st[i].regno);
        printf("name: %s\n", st[i].na);
        printf("branch: %s\n",st[i].br);
        printf("date of Birth %s\n",st[i].dob);
        printf("marks of 5 Subjects\n");
        for (int j = 0;j<n;j++) {
            printf("%d ",st[i].ma[j]);
        }
        printf("\noverall Grade: %c\n", st[i].gr);
    }

    return 0;
}

#include <stdio.h>
#include <stdlib.h>
#include <limits.h> // for INT_MIN

#define MAX_SIZE 100 // Maximum size of stack

int stack[MAX_SIZE];
int top = -1;

// Function prototypes
void push(int element);
int pop();
void sortStack();

int main() {
   // Example stack: 7, 3, 9, 2, 8, 1
   push(7);
   push(3);
   push(9);
   push(2);
   push(8);
   push(1);
   
   sortStack();
   
   // Sorted stack: 1, 2, 3, 7, 8, 9
   printf("Sorted stack:\n");
   while(top != -1) {
      printf("%d\n", pop());
   }
   
   return 0;
}

void push(int element) {
   if(top == MAX_SIZE-1) {
      printf("Stack overflow!");
      exit(1);
   }
   top++;
   stack[top] = element;
}

int pop() {
   if(top == -1) {
      printf("Stack underflow!");
      exit(1);
   }
   int element = stack[top];
   top--;
   return element;
}

void sortStack() {
   int tempStack[MAX_SIZE];
   int tempTop = -1;
   while(top != -1) 
{
      int currentElement = pop();
      while(tempTop != -1 && currentElement < tempStack[tempTop]) 
	{
         push(tempStack[tempTop]);
         tempTop--;
      }
      //tempTop++;
      tempStack[++tempTop] = currentElement;
   }
   // Copy elements from tempStack to stack
   while(tempTop != -1)
{
      push(tempStack[tempTop]);
      tempTop--;
}
}

#include<stdio.h>
#include<stdlib.h>
#define max 10
int stack_arr[max];
int top=-1;
int isfull()
{
	if(top==max-1)
		return 1;
	else
		return 0;
}
int isempty()
{
	if( top ==-1 )
		return 1;
	else
	 	return 0;
}
void push(int item)
{
	if( isfull() )
	{
		printf("stack overflow");
		return;
	}	
	top=top+1;
	stack_arr[top]=item;
}
int pop()
{
	int item;
	if( isempty() )
	{
		printf("\nStack Underflow\n");
		exit(1);
	}
	else
	{
		item = stack_arr[top];
		top--;
		return item;
	}
}
int peak()
{

	if(isempty() )
	{
	printf("\nStack Underflow\n");
	aexit(1);
	}
	return stack_arr[top];
}
void display()
{
	int i;
	if( isempty() )
	{
		printf("\nStack is empty\n");
		return;
	}
	printf("\nStack elements :\n\n");
	for(i=top;i>=0;i--)
		printf(" %d\n", stack_arr[i] );
 	printf("\n");
}
int main()
{
	int i,c,item,n,m;
	do
	{
	printf("Menu\n");
 	printf("1.Push\n");
 	printf("2.Pop\n");
 	printf("3.Display the top element\n");
 	printf("4.Display all stack elements\n");
 	printf("5.Quit\n");
 	printf("\nEnter your choice : ");
 	scanf("%d",&c);

		switch(c)
		{
			case 1 :
			printf("Enter no of elements you want to enter\n");
			scanf("%d",&n);
			for(i=0;i<n;i++)
			{
				printf("\nEnter the elements to be pushed : ");
				scanf("%d",&item);
				push(item);
			}
			break;
			case 2:
		
				printf("Enter no of elements to pop\n");
				scanf("%d",&n);
				for(i=0;i<n;i++)
				{
					item = pop();
					printf("Popped item is : %d\n",item );
					break;
				}
			case 3:
			printf("\nItem at the top is : %d\n", peak() );
			break;
			case 4:
			display();
			break;
			case 5:
			exit(1);
			default:
			printf("\nWrong choice\n");
		}
	}while(c!=5);
	return 0;	
}

#include<stdio.h>
int r()
{
	static int num=7;
	return num--;
}
int main()
{
	for(r();r();r())
	printf("%d",r());
	return 0;
}

#include<stdio.h>
int main()
{
	int a=-11;
	const int *p=&a;
	
	*p=10;
	printf("%d",a);
}

#include<stdio.h>
int comm(int);
int detail(int);
int comp(int);
struct dog
{
	char name[100];
	int lik,com;
}q[100],max,eq[100];

int main()
{
	int n;
	printf("ENTER THE NUMBER OF POST");
	scanf("%d",&n);
	printf("\n");
	detail(n);
	comp(n);
	return 0;
}


int detail(int n)
{
	int i=0;
	for(i=0;i<n;i++)
	{
		printf("\nNAME :");
		scanf("%s",q[i].name);
		printf("LIKES :");
		scanf("%d",&q[i].lik);
		printf("COMMENTS :");
		scanf("%d",&q[i].com);
		
	}
	return 0;
}
int comp(int n)
{
	int c=0,i=0,j=0;
	max.lik=q[0].lik;
	for(i=0;i<n;i++)
	{
			if(max.lik<q[i].lik)
			{
				max=q[i];
			}
	}
	for(i=0;i<n;i++)
	{
		if(max.lik==q[i].lik)
		{
			eq[j]=q[i];
			c++;
			j++;
		}
		
	}
	
	if(c!=0)
	{
		comm(n);
	}
	else
	{
		printf("%s IS THE GREATEST NUMBER OF LIKES",max.name);
	}
			
}
int comm(int n)
{
	int i=0,p=0;
	max.com=q[0].com;
	
	if(eq[0].com<eq[1].com)
	{
		printf("%s HAS THE GREATEST NUMBER OF LIKES",eq[1].name);
	}
	else
	{
		printf("%s HAS THE GREATEST NUMBER OF LIKES",eq[0].name);
	}
		 
}

#include <stdio.h>

int main()
{
    int arr[100],p,i,n,val;
    
    printf("Enter number of elements in the array\n");
    scanf("%d", &n);
    
    printf("Enter elements\n");
    
    for (i=0;i<n;i++)    
        scanf("%d", &arr[i]);
    
    printf("enter the location where you want to enter a new element\n");
    scanf("%d", &p);
    
    printf("Please enter the value\n");
    scanf("%d", &val);
    
    for (i=n-1;i>=p-1;i--)    
        arr[i+1] = arr[i];
    
    arr[p-1] = val;
    
    printf("array is\n");
    
    for (i=0;i<=n;i++)    
        printf("%d\n", arr[i]);    
    
    return 0;
}

#include <stdio.h>
int main()
{
    int arr[100],p,i,n,val;
    
    printf("Enter number of elements in the array\n");
    scanf("%d", &n);
    
    printf("Enter elements\n");
    
    for (i=0;i<n;i++)    
        scanf("%d", &arr[i]);
    
    printf("enter the location where you want to update a  element\n");
    scanf("%d", &p);
    
    printf("Please enter the value\n");
    scanf("%d", &val);
    arr[p-1]=val;
    printf("array after updation");
    for(i=0;i<n;i++)
    {
    	printf("\narr[%d]=%d",i,arr[i]);
	}
	return 0;
}

#include<stdio.h>
int main()
{
  int arr[100];
  int n,i,s,p=0;
  printf("no of elements in array\n");
  scanf("%d",&n);
  printf("enter the elements");
  for(i=0;i<n;i++)
    {
      scanf("%d",&arr[i]);
    }
  printf("enter element to search");
  scanf("%d",&s);
  for(i=0;i<n;i++)
  {
    if(arr[i]==s)
    {
      p=1;
    }
  }
  if(p==1)
  {
    printf("%d is found in array",s);
        
  }
  else
  {
    printf("%d was not found",s);
  }
   
}

#include <stdio.h>
int main() 
{
  int arr[100],arsize;
  int n,i;
  printf("no of elements in array\n");
  scanf("%d",&n);
  printf("enter the elements");
  for(i=0;i<n;i++)
    {
      scanf("%d",&arr[i]);
    }
  arsize=sizeof(arr)/sizeof(arr[0]);
  printf("the original array elements are:\n");
    for(i=0;i<n;i++)
    {
      printf("arr[%d]=%d\n",i,arr[i]);
    }
  return 0;
}

#include<stdio.h>
int main()
{
   int arr[100],arsize;
  int n,i,p;
  printf("no of elements in array\n");
  scanf("%d",&n);
  printf("enter the elements");
  for(i=0;i<n;i++)
    {
      scanf("%d",&arr[i]);
    }
  printf("position to delete");
  scanf("%d",&p);
  if (p>= n+1 )    
    printf("Deletion not possible.\n");
    
    else
    {    
        for (i=p-1 ;i< n-1;i++ ) 
        arr[i] = arr[i+1];        
        
        printf("array is\n");
        
        for(i=0;i<n-1;i++)        
        printf("%d\n", arr[i]);        
    }    
}
*/
#include<stdio.h>
int main()
{
    int arr[100],p,i,n,val;
    
    printf("Enter number of elements in the array\n");
    scanf("%d", &n);
    
    printf("Enter elements\n");
    
    for (i=0;i<n;i++)    
        scanf("%d", &arr[i]);
    
    printf("enter the location where you want to enter a new element\n");
    scanf("%d", &p);
    
    printf("Please enter the value\n");
    scanf("%d", &val);
    
    for (i=n-1;i>=p-1;i--)    
        arr[i+1] = arr[i];
    
    arr[p-1] = val;
    
    printf("array is\n");
    
    for (i=0;i<=n;i++)    
        printf("%d\n", arr[i]);    
    
    return 0;
}
