#include<stdio.h>
#include<string.h>
void delet(char string[],char substr[])
{
	int i;
	int str_len=strlen(string);
	int substr_len=strlen(substr);
	while(i<str_len)
	{
		if(strstr(&string[i],substr)==&string[i])
		{
		
			str_len-=substr_len;
		
		for(int j=i;j<str_len;j++)
		string[j]=string[j+substr_len];
		}
		else
		i++;
	}
}
int main()
{
	char string[100],sub[20];
	scanf("%[^n]s",string);
	scanf("%s",sub);
	delet(string,sub);
	printf("after-%s",string);
	return 0;
}
